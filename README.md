# Excel表格耗材与项目信息处理工具

这是一个精简的工具集，专门用于处理Excel表格中的耗材与项目信息，通过API查询并更新相关数据。

## 安装依赖

```bash
pip install -r requirements.txt
```

## 核心功能

### 1. 快速更新HC表格

主要功能：读取hc.xlsx文件，查询项目和耗材信息，并更新表格数据。

```bash
python fast_query_and_update_hc.py
```

**功能特点：**
- 自动读取hc.xlsx文件中的"主数据"工作表
- 批量查询项目编码对应的项目ID和名称
- 批量查询耗材编码对应的耗材ID、名称、单位、规格
- 生成带时间戳的更新后文件
- 支持并发查询，提高处理效率
- 自动生成详细的更新报告

### 2. 单个查询功能

#### 项目信息查询
```bash
python api_request.py
```

#### 耗材信息查询
```bash
python api_goods_request.py [关键字]
```

## 文件说明

- `fast_query_and_update_hc.py` - 核心Excel处理工具
- `api_request.py` - 项目信息API查询
- `api_goods_request.py` - 耗材信息API查询
- `hc.xlsx` - 主要的Excel数据文件
- `requirements.txt` - 项目依赖

## API接口

- 项目信息API：https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service
- 耗材信息API：https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/goods
- 认证方式：JWT token

## 使用说明

1. 确保hc.xlsx文件存在且包含"主数据"工作表
2. 工作表应包含"项目编码"和"耗材编码"列
3. 运行fast_query_and_update_hc.py进行批量更新
4. 查看生成的更新文件和报告