import requests
import json
import sys
from pprint import pprint

def get_new_token():
    """
    通过API获取新的token
    
    Returns:
        str: 新获取的token，如果失败则返回None
    """
    # API接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    
    # 请求参数
    data = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    print("=== 获取新的Token ===")
    print(f"请求URL: {url}")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        # 打印响应状态码
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应 - 接受200和201状态码
        if response.status_code in [200, 201]:
            try:
                result = response.json()
                
                # 检查是否包含accessToken
                if "accessToken" in result:
                    token = result["accessToken"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                # 检查是否包含token
                elif "token" in result:
                    token = result["token"]
                    print("Token获取成功!")
                    print(f"Token: {token[:20]}...")  # 只显示前20个字符
                    return token
                else:
                    print("响应中没有找到token或accessToken字段")
                    print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    return None
            except json.JSONDecodeError:
                print(f"无法解析响应内容: {response.text}")
                return None
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def call_api(keyword="250703002651"):
    """
    调用API接口获取产品服务信息
    
    Args:
        keyword (str): 查询关键字，默认为"250703002651"
        
    Returns:
        dict: API响应的JSON数据，如果请求失败则返回None
    """
    # 获取新的token
    token = get_new_token()
    if not token:
        print("获取Token失败，无法继续请求")
        return None
    
    # API 接口URL
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service"
    
    # 请求参数
    params = {
        "keyword": keyword
    }
    
    # 使用x-access-token作为请求头参数名
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    print("\n=== 请求信息 ===")
    print(f"请求URL: {url}")
    print(f"请求参数: {params}")
    print(f"认证方式: x-access-token")
    
    try:
        # 发送GET请求
        response = requests.get(url, params=params, headers=headers, timeout=10)
        
        # 打印响应状态码
        print("\n=== 响应信息 ===")
        print(f"状态码: {response.status_code}")
        
        # 尝试解析JSON响应
        if response.status_code == 200:
            result = response.json()
            print("请求成功!")
            return result
        else:
            print(f"请求失败: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None

def display_result(data):
    """
    格式化显示API返回的结果
    
    Args:
        data (dict): API返回的JSON数据
    """
    if not data:
        print("\n没有获取到数据")
        return
    
    print("\n=== 产品服务信息 ===")
    
    if "content" in data and data["content"]:
        for item in data["content"]:
            print(f"\n商品名称: {item.get('name', '未知')}")
            print(f"商品编码: {item.get('code', '未知')}")
            print(f"英文名称: {item.get('englishName') or '无'}")
            
            # 显示分类信息
            category = item.get("category", {})
            print(f"分类: {category.get('name') if category else '无'}")
            
            # 显示规格和单位
            print(f"规格: {item.get('spec') or '无'}")
            print(f"单位: {item.get('unit') or '无'}")
            
            print(f"价格: {item.get('price')} 元")
            print(f"启用状态: {'是' if item.get('enable') else '否'}")
            
            # 显示物料信息
            materials = item.get("materialDtos", [])
            if materials:
                print("\n物料清单:")
                for idx, material in enumerate(materials, 1):
                    print(f"  {idx}. {material.get('materialName', '未知')} - "
                          f"规格: {material.get('spec', '未知')}, "
                          f"单位: {material.get('unit', '未知')}, "
                          f"数量: {material.get('usedQuantity', 0)}")
            else:
                print("\n物料清单: 无")
    else:
        print("未找到产品服务信息")
    
    # 显示分页信息
    if "totalElements" in data:
        print(f"\n总记录数: {data.get('totalElements')}")
        print(f"总页数: {data.get('totalPages')}")

if __name__ == "__main__":
    # 从命令行获取关键字参数
    keyword = "250703002651"  # 默认关键字
    if len(sys.argv) > 1:
        keyword = sys.argv[1]
        print(f"使用关键字: {keyword}")
    
    # 调用API获取数据
    result = call_api(keyword)
    
    # 显示格式化结果
    if result:
        display_result(result)
        
        # 保存完整响应到文件
        with open("api_response.json", "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print("\n完整响应已保存到 api_response.json 文件") 