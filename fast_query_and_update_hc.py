import pandas as pd
import requests
import json
import time
from datetime import datetime
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
import threading

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hc_fast_update.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 全局缓存和锁
project_cache = {}
goods_cache = {}
cache_lock = Lock()
request_count = 0
request_lock = Lock()

def get_new_token():
    """获取新的token"""
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    
    data = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    headers = {"Content-Type": "application/json"}
    
    try:
        response = requests.post(url, json=data, headers=headers, timeout=10)
        
        if response.status_code in [200, 201]:
            result = response.json()
            if "accessToken" in result:
                return result["accessToken"]
            elif "token" in result:
                return result["token"]
        
        logging.error(f"获取token失败，状态码: {response.status_code}")
        return None
        
    except Exception as e:
        logging.error(f"获取token异常: {e}")
        return None

def query_project_info_cached(project_code, token):
    """查询项目信息（带缓存）"""
    global request_count
    
    # 检查缓存
    with cache_lock:
        if project_code in project_cache:
            return project_cache[project_code]
    
    # 增加请求计数
    with request_lock:
        request_count += 1
        current_count = request_count
        if current_count % 50 == 0:
            logging.info(f"已发送 {current_count} 个API请求")
    
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service"
    
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    params = {"keyword": project_code}
    
    try:
        response = requests.get(url, headers=headers, params=params, timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            
            if "content" in result:
                projects = result["content"]
                
                # 查找精确匹配
                for project in projects:
                    if project.get("code") == project_code:
                        project_info = {
                            "id": project.get("id"),
                            "name": project.get("name"),
                            "code": project.get("code")
                        }
                        
                        # 缓存结果
                        with cache_lock:
                            project_cache[project_code] = project_info
                        
                        return project_info
                
                # 如果没有精确匹配，返回第一个结果
                if projects:
                    project = projects[0]
                    project_info = {
                        "id": project.get("id"),
                        "name": project.get("name"),
                        "code": project.get("code")
                    }
                    
                    # 缓存结果
                    with cache_lock:
                        project_cache[project_code] = project_info
                    
                    return project_info
                
                # 缓存空结果
                with cache_lock:
                    project_cache[project_code] = None
                
                return None
            else:
                with cache_lock:
                    project_cache[project_code] = None
                return None
                
        else:
            with cache_lock:
                project_cache[project_code] = None
            return None
            
    except Exception as e:
        logging.error(f"查询项目信息异常: {e}, 项目编码: {project_code}")
        with cache_lock:
            project_cache[project_code] = None
        return None

def query_goods_info_cached(goods_code, token):
    """查询耗材信息（带缓存）"""
    global request_count
    
    # 检查缓存
    with cache_lock:
        if goods_code in goods_cache:
            return goods_cache[goods_code]
    
    # 增加请求计数
    with request_lock:
        request_count += 1
        current_count = request_count
        if current_count % 50 == 0:
            logging.info(f"已发送 {current_count} 个API请求")
    
    url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/goods"
    
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    params = {"keyword": goods_code}
    
    try:
        response = requests.get(url, headers=headers, params=params, timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            
            if "content" in result:
                goods_list = result["content"]
                
                # 查找精确匹配
                for goods in goods_list:
                    if goods.get("code") == goods_code:
                        goods_info = {
                            "id": goods.get("id"),
                            "name": goods.get("name"),
                            "unit": goods.get("unit"),
                            "spec": goods.get("spec"),
                            "code": goods.get("code")
                        }
                        
                        # 缓存结果
                        with cache_lock:
                            goods_cache[goods_code] = goods_info
                        
                        return goods_info
                
                # 如果没有精确匹配，返回第一个结果
                if goods_list:
                    goods = goods_list[0]
                    goods_info = {
                        "id": goods.get("id"),
                        "name": goods.get("name"),
                        "unit": goods.get("unit"),
                        "spec": goods.get("spec"),
                        "code": goods.get("code")
                    }
                    
                    # 缓存结果
                    with cache_lock:
                        goods_cache[goods_code] = goods_info
                    
                    return goods_info
                
                # 缓存空结果
                with cache_lock:
                    goods_cache[goods_code] = None
                
                return None
            else:
                with cache_lock:
                    goods_cache[goods_code] = None
                return None
                
        else:
            with cache_lock:
                goods_cache[goods_code] = None
            return None
            
    except Exception as e:
        logging.error(f"查询耗材信息异常: {e}, 耗材编码: {goods_code}")
        with cache_lock:
            goods_cache[goods_code] = None
        return None

def query_unique_codes(unique_codes, query_func, token, max_workers=10):
    """并发查询唯一编码"""
    results = {}
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_code = {
            executor.submit(query_func, code, token): code 
            for code in unique_codes
        }
        
        # 收集结果
        completed = 0
        total = len(unique_codes)
        
        for future in as_completed(future_to_code):
            code = future_to_code[future]
            try:
                result = future.result()
                results[code] = result
                completed += 1
                
                if completed % 20 == 0:
                    logging.info(f"查询进度: {completed}/{total} ({completed/total*100:.1f}%)")
                
            except Exception as e:
                logging.error(f"查询编码 {code} 时出错: {e}")
                results[code] = None
    
    return results

def fast_update_hc_table():
    """快速更新HC表格"""
    logging.info("开始快速更新HC表格...")
    start_time = time.time()
    
    try:
        # 读取Excel文件
        logging.info("读取hc.xlsx文件...")
        df = pd.read_excel('hc.xlsx', sheet_name='主数据')
        
        logging.info(f"读取到 {len(df)} 条记录")
        
        # 获取token
        token = get_new_token()
        if not token:
            logging.error("无法获取token，程序退出")
            return
        
        # 获取唯一的项目编码和耗材编码
        unique_projects = df['项目编码'].unique()
        unique_goods = df['耗材编码'].unique()
        
        logging.info(f"发现 {len(unique_projects)} 个唯一项目编码")
        logging.info(f"发现 {len(unique_goods)} 个唯一耗材编码")
        
        # 并发查询项目信息
        logging.info("开始并发查询项目信息...")
        project_start = time.time()
        project_results = query_unique_codes(unique_projects, query_project_info_cached, token, max_workers=15)
        project_time = time.time() - project_start
        logging.info(f"项目查询完成，耗时: {project_time:.2f}秒")
        
        # 并发查询耗材信息
        logging.info("开始并发查询耗材信息...")
        goods_start = time.time()
        goods_results = query_unique_codes(unique_goods, query_goods_info_cached, token, max_workers=15)
        goods_time = time.time() - goods_start
        logging.info(f"耗材查询完成，耗时: {goods_time:.2f}秒")
        
        # 更新DataFrame
        logging.info("更新表格数据...")
        
        # 添加新列（如果不存在）
        new_columns = ['项目id', '项目名称', '耗材id', '耗材名称', '耗材单位', '耗材规格']
        for col in new_columns:
            if col not in df.columns:
                df[col] = None
        
        # 批量更新数据
        update_start = time.time()
        
        for index, row in df.iterrows():
            project_code = row['项目编码']
            goods_code = row['耗材编码']
            
            # 更新项目信息
            if project_code in project_results and project_results[project_code]:
                project_info = project_results[project_code]
                df.at[index, '项目id'] = project_info['id']
                df.at[index, '项目名称'] = project_info['name']
            
            # 更新耗材信息
            if goods_code in goods_results and goods_results[goods_code]:
                goods_info = goods_results[goods_code]
                df.at[index, '耗材id'] = goods_info['id']
                df.at[index, '耗材名称'] = goods_info['name']
                df.at[index, '耗材单位'] = goods_info['unit']
                df.at[index, '耗材规格'] = goods_info['spec']
        
        update_time = time.time() - update_start
        logging.info(f"数据更新完成，耗时: {update_time:.2f}秒")
        
        # 保存更新后的文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f'hc_fast_updated_{timestamp}.xlsx'
        
        # 创建新的Excel文件
        save_start = time.time()
        with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
            # 写入更新后的主数据
            df.to_excel(writer, sheet_name='主数据', index=False)
            
            # 复制其他工作表
            try:
                excel_file = pd.ExcelFile('hc.xlsx')
                for sheet_name in excel_file.sheet_names:
                    if sheet_name != '主数据':
                        sheet_df = pd.read_excel('hc.xlsx', sheet_name=sheet_name)
                        sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)
            except Exception as e:
                logging.warning(f"复制其他工作表时出错: {e}")
        
        save_time = time.time() - save_start
        logging.info(f"文件保存完成，耗时: {save_time:.2f}秒")
        
        total_time = time.time() - start_time
        logging.info(f"总耗时: {total_time:.2f}秒")
        logging.info(f"更新完成！文件已保存为: {output_filename}")
        
        # 生成更新报告
        generate_fast_update_report(project_results, goods_results, df, timestamp, total_time)
        
    except Exception as e:
        logging.error(f"更新过程中出错: {e}")
        raise

def generate_fast_update_report(project_results, goods_results, df, timestamp, total_time):
    """生成快速更新报告"""
    report_filename = f'hc_fast_update_report_{timestamp}.txt'
    
    # 统计更新情况
    project_found = sum(1 for info in project_results.values() if info is not None)
    project_total = len(project_results)
    
    goods_found = sum(1 for info in goods_results.values() if info is not None)
    goods_total = len(goods_results)
    
    # 统计数据完整性
    project_id_filled = df['项目id'].notna().sum()
    project_name_filled = df['项目名称'].notna().sum()
    goods_id_filled = df['耗材id'].notna().sum()
    goods_name_filled = df['耗材名称'].notna().sum()
    goods_unit_filled = df['耗材单位'].notna().sum()
    goods_spec_filled = df['耗材规格'].notna().sum()
    
    total_rows = len(df)
    
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write("HC表格快速更新报告\n")
        f.write("=" * 50 + "\n")
        f.write(f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总耗时: {total_time:.2f}秒\n")
        f.write(f"总API请求数: {request_count}\n")
        f.write(f"平均请求速度: {request_count/total_time:.2f} 请求/秒\n\n")
        
        f.write("一、查询统计\n")
        f.write("-" * 30 + "\n")
        f.write(f"项目查询: {project_found}/{project_total} ({project_found/project_total*100:.1f}%)\n")
        f.write(f"耗材查询: {goods_found}/{goods_total} ({goods_found/goods_total*100:.1f}%)\n\n")
        
        f.write("二、数据填充情况\n")
        f.write("-" * 30 + "\n")
        f.write(f"项目ID填充: {project_id_filled}/{total_rows} ({project_id_filled/total_rows*100:.1f}%)\n")
        f.write(f"项目名称填充: {project_name_filled}/{total_rows} ({project_name_filled/total_rows*100:.1f}%)\n")
        f.write(f"耗材ID填充: {goods_id_filled}/{total_rows} ({goods_id_filled/total_rows*100:.1f}%)\n")
        f.write(f"耗材名称填充: {goods_name_filled}/{total_rows} ({goods_name_filled/total_rows*100:.1f}%)\n")
        f.write(f"耗材单位填充: {goods_unit_filled}/{total_rows} ({goods_unit_filled/total_rows*100:.1f}%)\n")
        f.write(f"耗材规格填充: {goods_spec_filled}/{total_rows} ({goods_spec_filled/total_rows*100:.1f}%)\n\n")
        
        f.write("三、性能优化效果\n")
        f.write("-" * 30 + "\n")
        f.write("- 使用并发查询，最大15个线程\n")
        f.write("- 实现智能缓存，避免重复查询\n")
        f.write("- 优化超时设置，提高响应速度\n")
        f.write(f"- 相比串行查询，预计节省时间: {(project_total + goods_total) * 1.5 - total_time:.2f}秒\n\n")
        
        f.write("四、未找到的编码\n")
        f.write("-" * 30 + "\n")
        missing_projects = [code for code, info in project_results.items() if info is None]
        missing_goods = [code for code, info in goods_results.items() if info is None]
        
        f.write(f"未找到的项目编码数量: {len(missing_projects)}\n")
        f.write(f"未找到的耗材编码数量: {len(missing_goods)}\n")
    
    logging.info(f"快速更新报告已保存到: {report_filename}")

if __name__ == "__main__":
    fast_update_hc_table()
