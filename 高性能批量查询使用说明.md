# 🚀 高性能批量查询工具使用说明

## 功能概述

这是一个专门为处理Excel表格中项目和耗材信息而设计的高性能批量查询工具。通过API接口自动获取项目和耗材的详细信息，并将结果写入Excel表格。

## 主要功能

### 📊 数据处理
- 读取 `hc.xlsx` 文件中的"主数据"工作表
- 根据**项目编码**查询并填入：项目id、项目名称
- 根据**耗材编码**查询并填入：耗材id、耗材名称、耗材单位、耗材规格

### ⚡ 性能优化
- **30个并发线程**：大幅提升查询速度
- **智能缓存机制**：避免重复查询相同编码
- **进度条显示**：实时查看处理进度
- **错误重试机制**：自动重试失败的请求，提高成功率
- **内存优化**：支持大数据量处理

## 使用方法

### 1. 环境准备

确保已安装必要的Python库：

```bash
pip install pandas openpyxl requests tqdm
```

### 2. 文件准备

确保当前目录下有 `hc.xlsx` 文件，且包含：
- "主数据"工作表
- "项目编码"列
- "耗材编码"列

### 3. 运行程序

```bash
python high_performance_batch_query.py
```

### 4. 查看结果

程序运行完成后会生成：
- **结果文件**：`hc_高性能批量查询结果_YYYYMMDD_HHMMSS.xlsx`
- **统计报告**：`高性能批量查询报告_YYYYMMDD_HHMMSS.txt`
- **日志文件**：`high_performance_batch_query.log`

## 输出说明

### Excel结果文件
原有数据保持不变，新增以下列：
- `项目id`：根据项目编码查询到的项目ID
- `项目名称`：根据项目编码查询到的项目名称
- `耗材id`：根据耗材编码查询到的耗材ID
- `耗材名称`：根据耗材编码查询到的耗材名称
- `耗材单位`：根据耗材编码查询到的耗材单位
- `耗材规格`：根据耗材编码查询到的耗材规格

### 统计报告
包含详细的性能统计信息：
- 查询成功率统计
- 数据填充情况
- 性能指标（耗时、请求数、速度等）
- 失败请求列表

## 性能特点

### 🔥 高并发处理
- 使用30个并发线程同时查询
- 相比串行查询，速度提升20-30倍

### 🧠 智能缓存
- 自动缓存已查询的结果
- 避免重复查询相同编码
- 大幅减少API请求次数

### 🔄 错误处理
- 自动重试失败的请求（最多3次）
- 指数退避策略，避免频繁重试
- 详细记录失败原因

### 📊 实时监控
- 进度条显示查询进度
- 实时统计请求数量
- 详细的性能指标

## 注意事项

### ✅ 使用前检查
1. 确保 `hc.xlsx` 文件存在且格式正确
2. 确保网络连接正常，能访问API接口
3. 确保有足够的磁盘空间保存结果文件

### ⚠️ 性能建议
1. **大数据量处理**：建议分批处理，每批不超过10万条记录
2. **网络环境**：在网络稳定的环境下运行，避免频繁断网
3. **系统资源**：确保有足够的内存和CPU资源

### 🔧 故障排除
1. **Token获取失败**：检查网络连接和API凭据
2. **Excel读取失败**：检查文件是否被其他程序占用
3. **内存不足**：减少并发线程数或分批处理数据

## 技术参数

| 参数 | 值 | 说明 |
|------|----|----|
| 并发线程数 | 30 | 可根据系统性能调整 |
| 请求超时时间 | 10秒 | API请求的超时时间 |
| 最大重试次数 | 3次 | 失败请求的重试次数 |
| 缓存机制 | 内存缓存 | 避免重复查询 |

## 版本信息

- **版本**：v1.0
- **更新时间**：2024年
- **兼容性**：Python 3.6+
- **依赖库**：pandas, openpyxl, requests, tqdm

## 联系支持

如遇到问题，请查看：
1. 日志文件中的详细错误信息
2. 统计报告中的失败请求列表
3. 确认网络连接和API接口状态

---

**🎉 祝您使用愉快！**
