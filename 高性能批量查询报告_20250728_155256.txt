🚀 高性能批量查询统计报告
================================================================================
查询时间: 2025-07-28 15:52:57
总耗时: 9.88 秒
总API请求数: 381
平均请求速度: 38.56 请求/秒
并发线程数: 30
更新记录数: 3658

📊 查询成功率统计
--------------------------------------------------
项目查询成功: 177/177 (100.0%)
耗材查询成功: 204/204 (100.0%)
总体成功率: 100.0%

📋 数据填充情况
--------------------------------------------------
项目ID填充: 1829/1829 (100.0%)
项目名称填充: 1829/1829 (100.0%)
耗材ID填充: 1829/1829 (100.0%)
耗材名称填充: 1829/1829 (100.0%)
耗材单位填充: 1829/1829 (100.0%)
耗材规格填充: 1829/1829 (100.0%)

⚡ 性能优化特性
--------------------------------------------------
✅ 30个并发线程，大幅提升查询速度
✅ 智能缓存机制，避免重复查询
✅ 进度条显示，实时查看处理进度
✅ 错误重试机制，提高查询成功率
✅ 内存优化，支持大数据量处理
✅ 详细的性能统计和错误日志


================================================================================
报告生成完成 ✅
