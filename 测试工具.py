#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能批量查询工具测试脚本
用于验证工具的各项功能是否正常
"""

import os
import sys
import pandas as pd
import requests
from datetime import datetime

def test_dependencies():
    """测试依赖库是否正确安装"""
    print("🔍 测试依赖库...")
    
    try:
        import pandas
        print(f"✅ pandas {pandas.__version__}")
    except ImportError:
        print("❌ pandas 未安装")
        return False
    
    try:
        import openpyxl
        print(f"✅ openpyxl {openpyxl.__version__}")
    except ImportError:
        print("❌ openpyxl 未安装")
        return False
    
    try:
        import requests
        print(f"✅ requests {requests.__version__}")
    except ImportError:
        print("❌ requests 未安装")
        return False
    
    try:
        import tqdm
        print(f"✅ tqdm {tqdm.__version__}")
    except ImportError:
        print("❌ tqdm 未安装")
        return False
    
    return True

def test_excel_file():
    """测试Excel文件是否存在且格式正确"""
    print("\n📄 测试Excel文件...")
    
    if not os.path.exists('hc.xlsx'):
        print("❌ hc.xlsx 文件不存在")
        return False
    
    try:
        df = pd.read_excel('hc.xlsx', sheet_name='主数据')
        print(f"✅ 成功读取Excel文件，共 {len(df)} 行数据")
        
        required_columns = ['项目编码', '耗材编码']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"❌ 缺少必要的列: {missing_columns}")
            return False
        
        print("✅ Excel文件格式正确")
        
        # 显示数据统计
        project_codes = df['项目编码'].dropna().nunique()
        goods_codes = df['耗材编码'].dropna().nunique()
        print(f"📊 唯一项目编码数: {project_codes}")
        print(f"📊 唯一耗材编码数: {goods_codes}")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")
        return False

def test_api_connection():
    """测试API连接是否正常"""
    print("\n🌐 测试API连接...")
    
    login_url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login"
    credentials = {
        "appId": "40e38a58-5c66-4120-997a-befd595e0e48",
        "code": "sh-zmyh",
        "secret": "MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5"
    }
    
    try:
        response = requests.post(
            login_url, 
            json=credentials, 
            headers={"Content-Type": "application/json"}, 
            timeout=10
        )
        
        if response.status_code in [200, 201]:
            result = response.json()
            token = result.get("accessToken") or result.get("token")
            if token:
                print("✅ API连接正常，Token获取成功")
                return True, token
            else:
                print("❌ Token获取失败，响应中没有找到token字段")
                return False, None
        else:
            print(f"❌ API连接失败，状态码: {response.status_code}")
            return False, None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ API连接异常: {e}")
        return False, None

def test_api_query(token):
    """测试API查询功能"""
    print("\n🔍 测试API查询功能...")
    
    # 测试项目查询
    project_url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service"
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    try:
        # 使用一个测试编码
        response = requests.get(
            project_url, 
            headers=headers, 
            params={"keyword": "test"}, 
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 项目查询API正常")
        else:
            print(f"⚠️ 项目查询API返回状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 项目查询API异常: {e}")
        return False
    
    # 测试耗材查询
    goods_url = "https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/goods"
    
    try:
        response = requests.get(
            goods_url, 
            headers=headers, 
            params={"keyword": "test"}, 
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 耗材查询API正常")
            return True
        else:
            print(f"⚠️ 耗材查询API返回状态码: {response.status_code}")
            return True  # 状态码非200但可能是正常的（比如没有匹配结果）
    except Exception as e:
        print(f"❌ 耗材查询API异常: {e}")
        return False

def test_file_permissions():
    """测试文件读写权限"""
    print("\n📁 测试文件权限...")
    
    try:
        # 测试创建临时文件
        test_filename = f"test_file_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(test_filename, 'w', encoding='utf-8') as f:
            f.write("测试文件权限")
        
        # 测试读取文件
        with open(test_filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 删除测试文件
        os.remove(test_filename)
        
        print("✅ 文件读写权限正常")
        return True
        
    except Exception as e:
        print(f"❌ 文件权限测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 高性能批量查询工具 - 系统测试")
    print("=" * 60)
    
    all_tests_passed = True
    
    # 1. 测试依赖库
    if not test_dependencies():
        all_tests_passed = False
    
    # 2. 测试Excel文件
    if not test_excel_file():
        all_tests_passed = False
    
    # 3. 测试API连接
    api_ok, token = test_api_connection()
    if not api_ok:
        all_tests_passed = False
    
    # 4. 测试API查询（如果连接正常）
    if api_ok and token:
        if not test_api_query(token):
            all_tests_passed = False
    
    # 5. 测试文件权限
    if not test_file_permissions():
        all_tests_passed = False
    
    # 显示测试结果
    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 所有测试通过！系统准备就绪")
        print("✅ 可以运行高性能批量查询工具")
    else:
        print("❌ 部分测试失败，请检查上述问题")
        print("⚠️ 建议解决问题后再运行批量查询工具")
    print("=" * 60)
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
