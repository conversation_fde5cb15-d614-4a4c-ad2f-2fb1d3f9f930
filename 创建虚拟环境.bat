@echo off
chcp 65001 >nul
title 创建Python虚拟环境

echo.
echo ========================================
echo    🐍 创建Python虚拟环境
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python，请先安装Python 3.6或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装
python --version

:: 创建虚拟环境
echo.
echo 📦 正在创建虚拟环境...
if exist "venv" (
    echo ⚠️ 虚拟环境已存在，是否重新创建？
    set /p choice="输入 y 重新创建，其他键跳过: "
    if /i "%choice%"=="y" (
        echo 删除现有虚拟环境...
        rmdir /s /q venv
        python -m venv venv
        echo ✅ 虚拟环境重新创建完成
    ) else (
        echo ⏭️ 跳过创建，使用现有虚拟环境
    )
) else (
    python -m venv venv
    echo ✅ 虚拟环境创建完成
)

:: 激活虚拟环境
echo.
echo 🔄 激活虚拟环境...
call venv\Scripts\activate.bat

:: 升级pip
echo.
echo 📈 升级pip...
python -m pip install --upgrade pip

:: 安装依赖
echo.
echo 📦 安装项目依赖...
pip install -r requirements.txt

:: 验证安装
echo.
echo 🔍 验证安装...
python -c "import pandas, openpyxl, requests, tqdm; print('✅ 所有依赖安装成功')"

echo.
echo ========================================
echo    🎉 虚拟环境设置完成！
echo ========================================
echo.
echo 使用说明：
echo 1. 每次使用前先激活虚拟环境：
echo    venv\Scripts\activate.bat
echo.
echo 2. 然后运行批量查询工具：
echo    python high_performance_batch_query.py
echo.
echo 3. 或者直接双击：启动高性能批量查询.bat
echo.
echo 4. 退出虚拟环境：
echo    deactivate
echo ========================================

pause
