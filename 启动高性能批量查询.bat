@echo off
chcp 65001 >nul
title 高性能批量查询工具

echo.
echo ========================================
echo    🚀 高性能批量查询工具
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python，请先安装Python
    pause
    exit /b 1
)

:: 检查hc.xlsx文件是否存在
if not exist "hc.xlsx" (
    echo ❌ 错误：未找到hc.xlsx文件
    echo 请确保hc.xlsx文件在当前目录下
    pause
    exit /b 1
)

:: 检查并安装依赖
echo 📦 检查依赖库...
pip show pandas >nul 2>&1
if errorlevel 1 (
    echo 正在安装pandas...
    pip install pandas
)

pip show openpyxl >nul 2>&1
if errorlevel 1 (
    echo 正在安装openpyxl...
    pip install openpyxl
)

pip show requests >nul 2>&1
if errorlevel 1 (
    echo 正在安装requests...
    pip install requests
)

pip show tqdm >nul 2>&1
if errorlevel 1 (
    echo 正在安装tqdm...
    pip install tqdm
)

echo ✅ 依赖检查完成
echo.

:: 运行主程序
echo 🚀 启动高性能批量查询...
echo.
python high_performance_batch_query.py

echo.
echo 程序执行完成！
pause
