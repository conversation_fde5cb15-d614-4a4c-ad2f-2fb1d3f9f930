# 使用示例

## 1. 快速更新HC表格

这是最常用的功能，用于批量更新Excel表格中的项目和耗材信息。

### 前提条件
- 确保 `hc.xlsx` 文件存在
- 文件中包含"主数据"工作表
- 工作表包含"项目编码"和"耗材编码"列

### 运行命令
```bash
python fast_query_and_update_hc.py
```

### 输出结果
- 生成带时间戳的更新文件：`hc_fast_updated_YYYYMMDD_HHMMSS.xlsx`
- 生成详细的更新报告：`hc_fast_update_report_YYYYMMDD_HHMMSS.txt`
- 控制台显示处理进度和统计信息

## 2. 单个项目查询

查询单个项目的详细信息。

### 运行命令
```bash
python api_request.py
```

### 交互式输入
程序会提示输入项目编码，然后显示查询结果。

## 3. 单个耗材查询

查询单个耗材的详细信息。

### 运行命令
```bash
python api_goods_request.py
```

### 带参数运行
```bash
python api_goods_request.py 关键字
```

## 注意事项

1. **网络连接**：确保能访问API接口
2. **Excel格式**：确保Excel文件格式正确
3. **列名匹配**：程序会自动识别包含"项目编码"、"耗材编码"等关键词的列
4. **处理时间**：大量数据处理可能需要较长时间，请耐心等待
5. **备份数据**：建议在处理前备份原始Excel文件

## 常见问题

### Q: 提示"无法获取token"
A: 检查网络连接，确保能访问API接口

### Q: Excel文件读取失败
A: 确保文件未被其他程序占用，且格式正确

### Q: 查询结果为空
A: 检查编码是否正确，或者该编码在系统中不存在
