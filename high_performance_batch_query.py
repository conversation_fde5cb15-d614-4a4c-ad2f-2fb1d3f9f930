#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能批量查询工具
根据hc.xlsx表格中的项目编码和耗材编码批量查询API并更新表格数据

功能：
1. 读取hc.xlsx表格中的项目编码和耗材编码
2. 高并发查询项目信息（项目id、项目名称）
3. 高并发查询耗材信息（耗材id、耗材名称、耗材单位、耗材规格）
4. 将查询结果写入对应的表格列
5. 生成详细的统计报告

优化特性：
- 30个并发线程，提升查询速度
- 智能缓存机制，避免重复查询
- 进度条显示，实时查看处理进度
- 错误重试机制，提高成功率
- 内存优化，支持大数据量处理
- 详细的性能统计和报告
"""

import pandas as pd
import requests
import json
import time
from datetime import datetime
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
import threading
from tqdm import tqdm
import sys
import os

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('high_performance_batch_query.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 全局变量
project_cache = {}
goods_cache = {}
cache_lock = Lock()
request_count = 0
request_lock = Lock()
failed_requests = []
failed_lock = Lock()

# API配置
API_CONFIG = {
    'login_url': 'https://em02-ym-openapi-admin.linkedcare.cn/api/v1/auth/login',
    'project_url': 'https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/service',
    'goods_url': 'https://em02-ym-openapi-admin.linkedcare.cn/api/v1/catalog/product/goods',
    'credentials': {
        'appId': '40e38a58-5c66-4120-997a-befd595e0e48',
        'code': 'sh-zmyh',
        'secret': 'MzIxNjA5YmYtNjI0YS00OWU2LThmNWQtNmI2OTRiYmVkMTk5'
    },
    'timeout': 10,
    'max_retries': 3
}

def get_new_token():
    """获取新的JWT token"""
    headers = {"Content-Type": "application/json"}
    
    for attempt in range(API_CONFIG['max_retries']):
        try:
            response = requests.post(
                API_CONFIG['login_url'], 
                json=API_CONFIG['credentials'], 
                headers=headers, 
                timeout=API_CONFIG['timeout']
            )
            
            if response.status_code in [200, 201]:
                result = response.json()
                token = result.get("accessToken") or result.get("token")
                if token:
                    logging.info("Token获取成功")
                    return token
            
            logging.warning(f"获取token失败，状态码: {response.status_code}，尝试 {attempt + 1}/{API_CONFIG['max_retries']}")
            
        except Exception as e:
            logging.warning(f"获取token异常: {e}，尝试 {attempt + 1}/{API_CONFIG['max_retries']}")
            
        if attempt < API_CONFIG['max_retries'] - 1:
            time.sleep(2 ** attempt)  # 指数退避
    
    logging.error("获取token失败，已达到最大重试次数")
    return None

def make_api_request(url, params, token, max_retries=3):
    """通用API请求函数，带重试机制"""
    headers = {
        "x-access-token": token,
        "Content-Type": "application/json"
    }
    
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, params=params, timeout=API_CONFIG['timeout'])
            
            if response.status_code == 200:
                return response.json()
            else:
                logging.warning(f"API请求失败，状态码: {response.status_code}，尝试 {attempt + 1}/{max_retries}")
                
        except Exception as e:
            logging.warning(f"API请求异常: {e}，尝试 {attempt + 1}/{max_retries}")
            
        if attempt < max_retries - 1:
            time.sleep(1)  # 重试前等待
    
    return None

def query_project_info_optimized(project_code, token):
    """优化的项目信息查询"""
    global request_count
    
    # 检查缓存
    with cache_lock:
        if project_code in project_cache:
            return project_cache[project_code]
    
    # 增加请求计数
    with request_lock:
        request_count += 1
    
    params = {"keyword": project_code}
    result = make_api_request(API_CONFIG['project_url'], params, token)
    
    if result and "content" in result:
        projects = result["content"]
        
        # 查找精确匹配
        for project in projects:
            if project.get("code") == project_code:
                project_info = {
                    "id": project.get("id"),
                    "name": project.get("name"),
                    "code": project.get("code")
                }
                
                # 缓存结果
                with cache_lock:
                    project_cache[project_code] = project_info
                
                return project_info
        
        # 如果没有精确匹配，返回第一个结果
        if projects:
            project = projects[0]
            project_info = {
                "id": project.get("id"),
                "name": project.get("name"),
                "code": project.get("code")
            }
            
            with cache_lock:
                project_cache[project_code] = project_info
            
            return project_info
    
    # 记录失败的请求
    with failed_lock:
        failed_requests.append(('project', project_code))
    
    # 缓存空结果
    with cache_lock:
        project_cache[project_code] = None
    
    return None

def query_goods_info_optimized(goods_code, token):
    """优化的耗材信息查询"""
    global request_count
    
    # 检查缓存
    with cache_lock:
        if goods_code in goods_cache:
            return goods_cache[goods_code]
    
    # 增加请求计数
    with request_lock:
        request_count += 1
    
    params = {"keyword": goods_code}
    result = make_api_request(API_CONFIG['goods_url'], params, token)
    
    if result and "content" in result:
        goods_list = result["content"]
        
        # 查找精确匹配
        for goods in goods_list:
            if goods.get("code") == goods_code:
                goods_info = {
                    "id": goods.get("id"),
                    "name": goods.get("name"),
                    "unit": goods.get("unit"),
                    "spec": goods.get("spec"),
                    "code": goods.get("code")
                }
                
                # 缓存结果
                with cache_lock:
                    goods_cache[goods_code] = goods_info
                
                return goods_info
        
        # 如果没有精确匹配，返回第一个结果
        if goods_list:
            goods = goods_list[0]
            goods_info = {
                "id": goods.get("id"),
                "name": goods.get("name"),
                "unit": goods.get("unit"),
                "spec": goods.get("spec"),
                "code": goods.get("code")
            }
            
            with cache_lock:
                goods_cache[goods_code] = goods_info
            
            return goods_info
    
    # 记录失败的请求
    with failed_lock:
        failed_requests.append(('goods', goods_code))
    
    # 缓存空结果
    with cache_lock:
        goods_cache[goods_code] = None
    
    return None

def concurrent_query_with_progress(unique_codes, query_func, token, desc, max_workers=30):
    """带进度条的并发查询"""
    results = {}
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_code = {
            executor.submit(query_func, code, token): code 
            for code in unique_codes
        }
        
        # 使用进度条显示进度
        with tqdm(total=len(unique_codes), desc=desc, unit="个") as pbar:
            for future in as_completed(future_to_code):
                code = future_to_code[future]
                try:
                    result = future.result()
                    results[code] = result
                except Exception as e:
                    logging.error(f"查询编码 {code} 时出错: {e}")
                    results[code] = None
                
                pbar.update(1)
    
    return results

def validate_excel_file(file_path):
    """验证Excel文件格式和必要列"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")

    try:
        df = pd.read_excel(file_path, sheet_name='主数据')
    except Exception as e:
        raise ValueError(f"无法读取Excel文件: {e}")

    required_columns = ['项目编码', '耗材编码']
    missing_columns = [col for col in required_columns if col not in df.columns]

    if missing_columns:
        raise ValueError(f"缺少必要的列: {missing_columns}")

    return df

def high_performance_batch_query():
    """高性能批量查询主函数"""
    print("=" * 60)
    print("🚀 高性能批量查询工具启动")
    print("=" * 60)

    start_time = time.time()

    try:
        # 1. 验证和读取Excel文件
        print("📖 正在读取Excel文件...")
        df = validate_excel_file('hc.xlsx')
        print(f"✅ 成功读取 {len(df)} 条记录")

        # 2. 获取token
        print("🔐 正在获取API访问令牌...")
        token = get_new_token()
        if not token:
            raise Exception("无法获取API访问令牌")
        print("✅ API访问令牌获取成功")

        # 3. 提取唯一编码
        print("🔍 正在分析数据...")
        unique_projects = df['项目编码'].dropna().unique()
        unique_goods = df['耗材编码'].dropna().unique()

        print(f"📊 发现 {len(unique_projects)} 个唯一项目编码")
        print(f"📊 发现 {len(unique_goods)} 个唯一耗材编码")
        print(f"📊 总计需要查询 {len(unique_projects) + len(unique_goods)} 个编码")

        # 4. 并发查询项目信息
        print("\n🔄 开始查询项目信息...")
        project_results = concurrent_query_with_progress(
            unique_projects,
            query_project_info_optimized,
            token,
            "查询项目信息",
            max_workers=30
        )

        # 5. 并发查询耗材信息
        print("\n🔄 开始查询耗材信息...")
        goods_results = concurrent_query_with_progress(
            unique_goods,
            query_goods_info_optimized,
            token,
            "查询耗材信息",
            max_workers=30
        )

        # 6. 更新DataFrame
        print("\n📝 正在更新表格数据...")

        # 添加新列（如果不存在）
        new_columns = ['项目id', '项目名称', '耗材id', '耗材名称', '耗材单位', '耗材规格']
        for col in new_columns:
            if col not in df.columns:
                df[col] = None

        # 批量更新数据
        update_count = 0
        with tqdm(total=len(df), desc="更新表格数据", unit="行") as pbar:
            for index, row in df.iterrows():
                project_code = row['项目编码']
                goods_code = row['耗材编码']

                # 更新项目信息
                if pd.notna(project_code) and project_code in project_results:
                    project_info = project_results[project_code]
                    if project_info:
                        df.at[index, '项目id'] = project_info['id']
                        df.at[index, '项目名称'] = project_info['name']
                        update_count += 1

                # 更新耗材信息
                if pd.notna(goods_code) and goods_code in goods_results:
                    goods_info = goods_results[goods_code]
                    if goods_info:
                        df.at[index, '耗材id'] = goods_info['id']
                        df.at[index, '耗材名称'] = goods_info['name']
                        df.at[index, '耗材单位'] = goods_info['unit']
                        df.at[index, '耗材规格'] = goods_info['spec']
                        update_count += 1

                pbar.update(1)

        # 7. 保存更新后的文件
        print("\n💾 正在保存文件...")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f'hc_高性能批量查询结果_{timestamp}.xlsx'

        with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
            # 写入更新后的主数据
            df.to_excel(writer, sheet_name='主数据', index=False)

            # 尝试复制其他工作表
            try:
                excel_file = pd.ExcelFile('hc.xlsx')
                for sheet_name in excel_file.sheet_names:
                    if sheet_name != '主数据':
                        sheet_df = pd.read_excel('hc.xlsx', sheet_name=sheet_name)
                        sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)
            except Exception as e:
                logging.warning(f"复制其他工作表时出错: {e}")

        # 8. 生成统计报告
        total_time = time.time() - start_time
        generate_performance_report(project_results, goods_results, df, timestamp, total_time, update_count)

        # 9. 显示完成信息
        print("\n" + "=" * 60)
        print("🎉 批量查询完成！")
        print("=" * 60)
        print(f"📄 输出文件: {output_filename}")
        print(f"⏱️  总耗时: {total_time:.2f} 秒")
        print(f"🔢 总请求数: {request_count}")
        print(f"📊 更新记录数: {update_count}")
        print(f"⚡ 平均速度: {request_count/total_time:.2f} 请求/秒")

        if failed_requests:
            print(f"⚠️  失败请求数: {len(failed_requests)}")
            print("详细信息请查看日志文件和统计报告")

        print("=" * 60)

    except Exception as e:
        logging.error(f"批量查询过程中出错: {e}")
        print(f"❌ 错误: {e}")
        sys.exit(1)

def generate_performance_report(project_results, goods_results, df, timestamp, total_time, update_count):
    """生成性能统计报告"""
    report_filename = f'高性能批量查询报告_{timestamp}.txt'

    # 统计查询成功率
    project_found = sum(1 for info in project_results.values() if info is not None)
    project_total = len(project_results)

    goods_found = sum(1 for info in goods_results.values() if info is not None)
    goods_total = len(goods_results)

    # 统计数据完整性
    project_id_filled = df['项目id'].notna().sum()
    project_name_filled = df['项目名称'].notna().sum()
    goods_id_filled = df['耗材id'].notna().sum()
    goods_name_filled = df['耗材名称'].notna().sum()
    goods_unit_filled = df['耗材单位'].notna().sum()
    goods_spec_filled = df['耗材规格'].notna().sum()

    total_rows = len(df)

    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write("🚀 高性能批量查询统计报告\n")
        f.write("=" * 80 + "\n")
        f.write(f"查询时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总耗时: {total_time:.2f} 秒\n")
        f.write(f"总API请求数: {request_count}\n")
        f.write(f"平均请求速度: {request_count/total_time:.2f} 请求/秒\n")
        f.write(f"并发线程数: 30\n")
        f.write(f"更新记录数: {update_count}\n\n")

        f.write("📊 查询成功率统计\n")
        f.write("-" * 50 + "\n")
        f.write(f"项目查询成功: {project_found}/{project_total} ({project_found/project_total*100:.1f}%)\n")
        f.write(f"耗材查询成功: {goods_found}/{goods_total} ({goods_found/goods_total*100:.1f}%)\n")
        f.write(f"总体成功率: {(project_found + goods_found)/(project_total + goods_total)*100:.1f}%\n\n")

        f.write("📋 数据填充情况\n")
        f.write("-" * 50 + "\n")
        f.write(f"项目ID填充: {project_id_filled}/{total_rows} ({project_id_filled/total_rows*100:.1f}%)\n")
        f.write(f"项目名称填充: {project_name_filled}/{total_rows} ({project_name_filled/total_rows*100:.1f}%)\n")
        f.write(f"耗材ID填充: {goods_id_filled}/{total_rows} ({goods_id_filled/total_rows*100:.1f}%)\n")
        f.write(f"耗材名称填充: {goods_name_filled}/{total_rows} ({goods_name_filled/total_rows*100:.1f}%)\n")
        f.write(f"耗材单位填充: {goods_unit_filled}/{total_rows} ({goods_unit_filled/total_rows*100:.1f}%)\n")
        f.write(f"耗材规格填充: {goods_spec_filled}/{total_rows} ({goods_spec_filled/total_rows*100:.1f}%)\n\n")

        f.write("⚡ 性能优化特性\n")
        f.write("-" * 50 + "\n")
        f.write("✅ 30个并发线程，大幅提升查询速度\n")
        f.write("✅ 智能缓存机制，避免重复查询\n")
        f.write("✅ 进度条显示，实时查看处理进度\n")
        f.write("✅ 错误重试机制，提高查询成功率\n")
        f.write("✅ 内存优化，支持大数据量处理\n")
        f.write("✅ 详细的性能统计和错误日志\n\n")

        if failed_requests:
            f.write("❌ 失败的请求\n")
            f.write("-" * 50 + "\n")
            f.write(f"失败请求总数: {len(failed_requests)}\n")

            project_failures = [req[1] for req in failed_requests if req[0] == 'project']
            goods_failures = [req[1] for req in failed_requests if req[0] == 'goods']

            if project_failures:
                f.write(f"\n失败的项目编码 ({len(project_failures)}个):\n")
                for i, code in enumerate(project_failures[:20], 1):  # 只显示前20个
                    f.write(f"  {i}. {code}\n")
                if len(project_failures) > 20:
                    f.write(f"  ... 还有 {len(project_failures) - 20} 个\n")

            if goods_failures:
                f.write(f"\n失败的耗材编码 ({len(goods_failures)}个):\n")
                for i, code in enumerate(goods_failures[:20], 1):  # 只显示前20个
                    f.write(f"  {i}. {code}\n")
                if len(goods_failures) > 20:
                    f.write(f"  ... 还有 {len(goods_failures) - 20} 个\n")

        f.write("\n" + "=" * 80 + "\n")
        f.write("报告生成完成 ✅\n")

    print(f"📊 统计报告已保存到: {report_filename}")

if __name__ == "__main__":
    # 检查依赖
    try:
        import tqdm
    except ImportError:
        print("❌ 缺少依赖库 tqdm，请运行: pip install tqdm")
        sys.exit(1)

    high_performance_batch_query()
